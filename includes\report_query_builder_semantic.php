<?php

/**
 * A self-contained utility class for Jalali (Shamsi) calendar conversions and calculations.
 */
class JalaliDate
{
    public static function gregorian_to_jalali($gy, $gm, $gd, $mod = '')
    {
        $g_d_m = array(0, 31, 59, 90, 120, 151, 181, 212, 243, 273, 304, 334);
        $jy = ($gy <= 1600) ? 0 : 979;
        $gy -= ($gy <= 1600) ? 621 : 1600;
        $gy2 = ($gm > 2) ? ($gy + 1) : $gy;
        $days = (365 * $gy) + ((int)(($gy2 + 3) / 4)) - ((int)(($gy2 + 99) / 100)) + ((int)(($gy2 + 399) / 400)) - 80 + $gd + $g_d_m[$gm - 1];
        $jy += 33 * ((int)($days / 12053));
        $days %= 12053;
        $jy += 4 * ((int)($days / 1461));
        $days %= 1461;
        $jy += (int)(($days - 1) / 365);
        if ($days > 365) $days = ($days - 1) % 365;
        $jm = ($days < 186) ? 1 + (int)($days / 31) : 7 + (int)(($days - 186) / 30);
        $jd = 1 + (($days < 186) ? ($days % 31) : (($days - 186) % 30));
        return ($mod == '') ? array($jy, $jm, $jd) : $jy . $mod . $jm . $mod . $jd;
    }

    public static function jalali_to_gregorian($jy, $jm, $jd, $mod = '')
    {
        $gy = ($jy <= 979) ? 621 : 1600;
        $jy -= ($jy <= 979) ? 0 : 979;
        $days = (365 * $jy) + (((int)($jy / 33)) * 8) + ((int)((($jy % 33) + 3) / 4)) + 78 + $jd + (($jm < 7) ? ($jm - 1) * 31 : (($jm - 7) * 30) + 186);
        $gy += 400 * ((int)($days / 146097));
        $days %= 146097;
        if ($days > 36524) {
            $gy += 100 * ((int)(--$days / 36524));
            $days %= 36524;
            if ($days >= 365) $days++;
        }
        $gy += 4 * ((int)($days / 1461));
        $days %= 1461;
        $gy += (int)(($days - 1) / 365);
        if ($days > 365) $days = ($days - 1) % 365;
        $gd = $days + 1;
        $sal_a = array(0, 31, (($gy % 4 == 0 && $gy % 100 != 0) || ($gy % 400 == 0)) ? 29 : 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31);
        for ($gm = 1; $gm <= 12 && $gd > $sal_a[$gm]; $gm++) $gd -= $sal_a[$gm];
        return ($mod == '') ? array($gy, $gm, $gd) : $gy . $mod . $gm . $mod . $gd;
    }
}


class SemanticReportQueryBuilder
{
    private array $layer;

    public function __construct()
    {
        $this->layer = [
            'dimensions' => [
                // دستگاه و موقعیت
                'device_id' => ['label' => 'کد دستگاه', 'table' => 'devices', 'column' => 'id', 'type' => 'number'],
                'device_name' => ['label' => 'نام دستگاه', 'table' => 'devices', 'column' => 'name', 'type' => 'text'],
                'location_name' => ['label' => 'محل استقرار', 'table' => 'locations', 'column' => 'location_name', 'type' => 'text'],
                // تکنسین و کاربر
                'technician_name' => ['label' => 'نام تکنسین', 'table' => 'technician', 'column' => 'name', 'type' => 'text'],
                // نوع و وضعیت دستورکار
                'work_order_type' => ['label' => 'نوع دستورکار', 'table' => 'work_orders', 'column' => 'type', 'type' => 'text'],
                'work_order_status' => ['label' => 'وضعیت دستورکار', 'table' => 'work_orders', 'column' => 'status', 'type' => 'text'],
                'priority' => ['label' => 'اولویت', 'table' => 'work_orders', 'column' => 'priority', 'type' => 'text'],
                // قطعه
                'part_name' => ['label' => 'نام قطعه', 'table' => 'work_order_parts', 'column' => 'part_name', 'type' => 'text'],
                
                // --- ابعاد تاریخ ---
                // Dimensions for date filtering
                'breakdown_date' => ['label' => 'تاریخ خرابی', 'table' => 'breakdown_reports', 'column' => 'breakdown_datetime', 'type' => 'date'],
                'work_order_request_date' => ['label' => 'تاریخ درخواست دستورکار', 'table' => 'work_orders', 'column' => 'request_date', 'type' => 'date'],

                // Jalali date dimensions for grouping
                'br_jalali_year' => ['label' => 'سال شمسی خرابی', 'expression' => "YEAR(CONVERT_TZ(breakdown_reports.breakdown_datetime, '+00:00', '+03:30'))-621", 'type' => 'text'],
                'br_jalali_month' => ['label' => 'ماه شمسی خرابی', 'expression' => "MONTH(CONVERT_TZ(breakdown_reports.breakdown_datetime, '+00:00', '+03:30'))", 'type' => 'text'],
                'br_jalali_day_of_week' => ['label' => 'روز هفته خرابی', 'expression' => "CASE DAYOFWEEK(breakdown_reports.breakdown_datetime) WHEN 7 THEN 'شنبه' WHEN 1 THEN 'یکشنبه' WHEN 2 THEN 'دوشنبه' WHEN 3 THEN 'سه شنبه' WHEN 4 THEN 'چهارشنبه' WHEN 5 THEN 'پنجشنبه' ELSE 'جمعه' END", 'type' => 'text'],
                'wo_jalali_year' => ['label' => 'سال شمسی دستورکار', 'expression' => "YEAR(CONVERT_TZ(work_orders.request_date, '+00:00', '+03:30'))-621", 'type' => 'text'],
                'wo_jalali_month' => ['label' => 'ماه شمسی دستورکار', 'expression' => "MONTH(CONVERT_TZ(work_orders.request_date, '+00:00', '+03:30'))", 'type' => 'text'],
                'wo_jalali_day_of_week' => ['label' => 'روز هفته دستورکار', 'expression' => "CASE DAYOFWEEK(work_orders.request_date) WHEN 7 THEN 'شنبه' WHEN 1 THEN 'یکشنبه' WHEN 2 THEN 'دوشنبه' WHEN 3 THEN 'سه شنبه' WHEN 4 THEN 'چهارشنبه' WHEN 5 THEN 'پنجشنبه' ELSE 'جمعه' END", 'type' => 'text'],
                
                // Other dimensions
                'problem_description' => ['label' => 'علت خرابی', 'table' => 'breakdown_reports', 'column' => 'problem_description', 'type' => 'text'],
                'line_stopped' => ['label' => 'توقف خط', 'table' => 'work_orders', 'column' => 'line_stopped', 'type' => 'boolean'],
            ],
            'measures' => [
                'breakdown_count' => ['label' => 'تعداد خرابی', 'expression' => 'COUNT(DISTINCT breakdown_reports.id)', 'tables' => ['breakdown_reports']],
                'mtbf_hours' => ['label' => 'MTBF (ساعت)', 'expression' => 'ROUND(TIMESTAMPDIFF(HOUR, MIN(breakdown_reports.breakdown_datetime), MAX(breakdown_reports.breakdown_datetime)) / NULLIF(COUNT(DISTINCT breakdown_reports.id)-1, 0), 2)', 'tables' => ['breakdown_reports']],
                'mttr_minutes' => ['label' => 'MTTR (دقیقه)', 'expression' => 'ROUND(AVG(TIMESTAMPDIFF(MINUTE, wo_exec.actual_start_datetime, wo_exec.actual_end_datetime)), 1)', 'tables' => ['wo_exec', 'work_orders']],
                'downtime_hours' => ['label' => 'ساعات توقف (Downtime)', 'expression' => 'ROUND(SUM(TIMESTAMPDIFF(HOUR, work_orders.stop_datetime, work_orders.restart_datetime)), 2)', 'tables' => ['work_orders']],
                'breakdown_with_line_stop_percent' => ['label' => 'درصد خرابی با توقف خط', 'expression' => 'ROUND(100 * SUM(CASE WHEN breakdown_reports.line_stoppage_datetime IS NOT NULL THEN 1 ELSE 0 END) / NULLIF(COUNT(breakdown_reports.id),0),1)', 'tables' => ['breakdown_reports']],
                'work_order_count' => ['label' => 'تعداد دستورکار', 'expression' => 'COUNT(DISTINCT work_orders.id)', 'tables' => ['work_orders']],
                'avg_wo_completion_time' => ['label' => 'میانگین زمان تکمیل دستورکار (دقیقه)', 'expression' => 'ROUND(AVG(TIMESTAMPDIFF(MINUTE, work_orders.request_date, wo_exec.actual_end_datetime)),1)', 'tables' => ['work_orders', 'wo_exec']],
                'technician_labor_hours' => ['label' => 'ساعات کار تکنسین', 'expression' => 'SUM(work_order_labor.hours_worked)', 'tables' => ['work_order_labor', 'work_orders', 'technician']],
                'parts_used_count' => ['label' => 'مصرف قطعات', 'expression' => 'SUM(work_order_parts.quantity_used)', 'tables' => ['work_order_parts', 'work_orders']],
                'poke_count' => ['label' => 'تعداد poke', 'expression' => 'COUNT(DISTINCT breakdown_report_pokes.id)', 'tables' => ['breakdown_report_pokes', 'breakdown_reports']],
                'pm_done_count' => ['label' => 'تعداد PM انجام‌شده', 'expression' => 'COUNT(DISTINCT logs.id)', 'tables' => ['logs']],
            ],
            'join_paths' => [
                'devices' => [
                    'locations' => 'devices.location = locations.id',
                    'work_orders' => 'devices.id = work_orders.device_id',
                    'breakdown_reports' => 'devices.id = breakdown_reports.device_id',
                ],
                'breakdown_reports' => [
                    'devices' => 'breakdown_reports.device_id = devices.id',
                    'reporter' => 'breakdown_reports.reported_by_id = reporter.id',
                    'breakdown_report_pokes' => 'breakdown_reports.id = breakdown_report_pokes.report_id',
                ],
                'work_orders' => [
                    'devices' => 'work_orders.device_id = devices.id',
                    'requester' => 'work_orders.requester_id = requester.id',
                    'wo_exec' => 'work_orders.id = wo_exec.work_order_id',
                    'work_order_assignees' => 'work_orders.id = work_order_assignees.work_order_id',
                    'activities' => 'work_orders.scheduled_activity_id = activities.id',
                    'work_order_labor' => 'work_orders.id = work_order_labor.work_order_id',
                    'work_order_parts' => 'work_orders.id = work_order_parts.work_order_id',
                ],
                'work_order_labor' => [
                    'work_orders' => 'work_order_labor.work_order_id = work_orders.id',
                    'technician' => 'work_order_labor.user_id = technician.id',
                ],
                'work_order_parts' => [
                    'work_orders' => 'work_order_parts.work_order_id = work_orders.id',
                ],
                'breakdown_report_pokes' => [
                    'breakdown_reports' => 'breakdown_report_pokes.report_id = breakdown_reports.id',
                ],
                'activities' => ['categories' => 'activities.category_id = categories.id'],
                'work_order_assignees' => ['technician' => 'work_order_assignees.user_id = technician.id'],
            ],
            'aliased_tables' => [
                'requester' => 'users',
                'reporter' => 'users',
                'technician' => 'users',
                'wo_exec' => 'work_order_execution',
            ]
        ];
    }

    public function getSemanticLayer()
    {
        $ui_layer = ['dimensions' => [], 'measures' => []];
        foreach ($this->layer['dimensions'] as $key => $val) {
            $ui_layer['dimensions'][$key] = ['label' => $val['label'], 'type' => $val['type']];
        }
        foreach ($this->layer['measures'] as $key => $val) {
            $ui_layer['measures'][$key] = ['label' => $val['label']];
        }
        return $ui_layer;
    }

    public function buildSql(array $definition): array
    {
        $rows = array_values(array_filter($definition['rows'] ?? [], 'is_string'));
        $columns = array_values(array_filter($definition['columns'] ?? [], 'is_string'));
        
        $filters = $definition['filters'] ?? [];
        $orderBy = $definition['orderBy'] ?? null;
        $limit = isset($definition['limit']) ? (int)$definition['limit'] : 1000;

        if (empty($columns) && empty($rows)) {
            throw new Exception("گزارش باید حداقل یک سطر (بعد) یا یک ستون (مقدار) داشته باشد.");
        }
        if (empty($rows) && count($columns) > 1 && ($definition['chartType'] ?? '') !== 'kpi') {
            throw new Exception("برای گزارش‌های چندستونه، حداقل یک بعد (سطر) لازم است. لطفاً یک بعد (مثلاً تاریخ یا نام دستگاه) انتخاب کنید.");
        }

        $is_listing_report = empty($columns);
        $params = [];
        $tables = [];
        $get_field_info = fn($key) => $this->layer['dimensions'][$key] ?? $this->layer['measures'][$key] ?? null;

        // Date range calculation
        $cp_range = null; // Current Period
        $date_filters = [];
        foreach ($filters as $key => $value) {
            $field = $get_field_info($key);
            if ($field && ($field['type'] ?? '') === 'date') {
                $date_filters[] = ['key' => $key, 'value' => $value, 'field' => $field];
            }
        }
        
        if (!empty($date_filters)) {
            $cp_range = $this->getDateRangeFromKeyword($date_filters[0]['value']);
        }

        // Collect tables
        $all_field_keys = array_merge($rows, $columns, array_keys($filters));
        if ($orderBy && isset($orderBy['key'])) $all_field_keys[] = $orderBy['key'];
        
        foreach ($all_field_keys as $key) {
            $field = $get_field_info($key);
            if (!$field) continue;
            if (isset($field['table'])) $tables[$field['table']] = true;
            if (isset($field['tables'])) foreach ($field['tables'] as $tbl) $tables[$tbl] = true;
        }

        // Build SELECT clause
        $selects = [];
        $select_prefix = $is_listing_report ? 'DISTINCT ' : '';
        $fields_to_select = $is_listing_report ? $rows : array_merge($rows, $columns);
        
        foreach ($fields_to_select as $key) {
            $field = $get_field_info($key);
            if (!$field) continue;

            if (isset($field['expression'])) {
                $selects[] = "{$field['expression']} AS `{$key}`";
            } else {
                $selects[] = "{$field['table']}.{$field['column']} AS `{$key}`";
            }
        }

        // Build WHERE clause
        $where_clauses = [];
        foreach ($filters as $key => $value) {
            if (empty($value)) continue;
            $field = $get_field_info($key);
            if (!$field) continue;

            if (($field['type'] ?? '') === 'date') {
                if ($cp_range) {
                    $date_column_ref = "{$field['table']}.{$field['column']}";
                    $where_clauses[] = "DATE({$date_column_ref}) BETWEEN ? AND ?";
                    array_push($params, $cp_range['start'], $cp_range['end']);
                }
            } else {
                 $col_ref = isset($field['expression']) ? $field['expression'] : "{$field['table']}.{$field['column']}";
                 if (is_array($value)) {
                     $placeholders = implode(',', array_fill(0, count($value), '?'));
                     $where_clauses[] = "{$col_ref} IN ({$placeholders})";
                     $params = array_merge($params, $value);
                 } else {
                     $where_clauses[] = "{$col_ref} LIKE ?";
                     $params[] = '%' . $value . '%';
                 }
            }
        }

        $table_keys = array_keys($tables);
        $from_table = $this->findBestFromTable($table_keys);
        $joins = $this->buildJoinClause($from_table, $table_keys);

        $sql_select = $select_prefix . implode(', ', $selects);
        $from_table_real = $this->layer['aliased_tables'][$from_table] ?? $from_table;
        $sql_from = "FROM {$from_table_real} AS {$from_table}";
        $sql_join = implode(' ', $joins);
        $sql_where = empty($where_clauses) ? '' : 'WHERE ' . implode(' AND ', $where_clauses);

        // Build GROUP BY clause
        $sql_group_by = '';
        if (!$is_listing_report && !empty($rows)) {
            $group_by_cols = [];
            foreach ($rows as $key) {
                $field = $get_field_info($key);
                if (!$field) continue;
                $group_by_cols[] = isset($field['expression']) ? "`{$key}`" : "{$field['table']}.{$field['column']}";
            }
            if(!empty($group_by_cols)){
                $sql_group_by = 'GROUP BY ' . implode(', ', $group_by_cols);
            }
        }

        // Build ORDER BY clause
        $sql_order_by = '';
        if ($orderBy && isset($orderBy['key'])) {
            $dir = strtoupper($orderBy['dir'] ?? 'ASC');
            if (!in_array($dir, ['ASC', 'DESC'])) $dir = 'ASC';
            $sql_order_by = "ORDER BY `{$orderBy['key']}` {$dir}";
        }

        $sql_limit = 'LIMIT ' . ($limit > 0 && $limit <= 5000 ? $limit : 1000);
        $sql = trim("SELECT {$sql_select} {$sql_from} {$sql_join} {$sql_where} {$sql_group_by} {$sql_order_by} {$sql_limit}");

        return ['sql' => $sql, 'params' => $params];
    }

    private function getDateRangeFromKeyword($filterValue)
    {
        $period = $filterValue['period'] ?? 'this_month';
        if ($period === 'custom') {
            return ['start' => $filterValue['start'] ?? date('Y-m-d'), 'end' => $filterValue['end'] ?? date('Y-m-d')];
        }

        list($gy, $gm, $gd) = explode('-', date('Y-m-d'));
        list($jy, $jm, $jd) = JalaliDate::gregorian_to_jalali($gy, $gm, $gd);
        $sy=$ey=$jy; $sm=$em=$jm; $sd=$ed=$jd;

        switch ($period) {
            case 'today':
                break;
            case 'yesterday':
                $ts = mktime(0, 0, 0, $gm, $gd - 1, $gy);
                return ['start' => date('Y-m-d', $ts), 'end' => date('Y-m-d', $ts)];
            case 'this_week':
                 $day_of_week = jddayofweek(cal_to_jd(CAL_GREGORIAN, $gm, $gd, $gy), 1); // Monday=1, Sunday=7
                 $start_ts = strtotime(($day_of_week-1) . ' days ago');
                 $end_ts = strtotime('+'.(7-$day_of_week).' days');
                 return ['start' => date('Y-m-d', $start_ts), 'end' => date('Y-m-d', $end_ts)];
            case 'last_week':
                 $day_of_week = jddayofweek(cal_to_jd(CAL_GREGORIAN, $gm, $gd, $gy), 1);
                 $start_ts = strtotime(($day_of_week+6) . ' days ago');
                 $end_ts = strtotime('+'.(0-$day_of_week).' days');
                 return ['start' => date('Y-m-d', $start_ts), 'end' => date('Y-m-d', $end_ts)];
            case 'this_month':
                list($sy, $sm, $sd) = [$jy, $jm, 1];
                $daysInMonth = ($jm <= 6) ? 31 : (($jm <= 11) ? 30 : ((((($jy - 474) % 2820) + 512) * 682) % 2816 < 682 ? 30 : 29));
                list($ey, $em, $ed) = [$jy, $jm, $daysInMonth];
                break;
            case 'last_month':
                $jm_prev = ($jm == 1) ? 12 : $jm - 1;
                $jy_prev = ($jm == 1) ? $jy - 1 : $jy;
                list($sy, $sm, $sd) = [$jy_prev, $jm_prev, 1];
                $daysInMonth = ($jm_prev <= 6) ? 31 : (($jm_prev <= 11) ? 30 : ((((($jy_prev - 474) % 2820) + 512) * 682) % 2816 < 682 ? 30 : 29));
                list($ey, $em, $ed) = [$jy_prev, $jm_prev, $daysInMonth];
                break;
            case 'this_year':
                list($sy, $sm, $sd) = [$jy, 1, 1];
                $isLeap = (((($jy - 474) % 2820) + 512) * 682) % 2816 < 682;
                list($ey, $em, $ed) = [$jy, 12, $isLeap ? 30 : 29];
                break;
            case 'last_year':
                $jy_prev = $jy - 1;
                list($sy, $sm, $sd) = [$jy_prev, 1, 1];
                $isLeap = (((($jy_prev - 474) % 2820) + 512) * 682) % 2816 < 682;
                list($ey, $em, $ed) = [$jy_prev, 12, $isLeap ? 30 : 29];
                break;
            case 'last_90_days':
                return ['start' => date('Y-m-d', strtotime('-90 days')), 'end' => date('Y-m-d')];
            default:
                return null;
        }
        list($start_g_y, $start_g_m, $start_g_d) = JalaliDate::jalali_to_gregorian($sy, $sm, $sd);
        list($end_g_y, $end_g_m, $end_g_d) = JalaliDate::jalali_to_gregorian($ey, $em, $ed);
        return [
            'start' => sprintf('%04d-%02d-%02d', $start_g_y, $start_g_m, $start_g_d),
            'end' => sprintf('%04d-%02d-%02d', $end_g_y, $end_g_m, $end_g_d)
        ];
    }

    private function findBestFromTable(array $tables): string
    {
        $priority = ['breakdown_reports', 'work_orders', 'activities', 'devices'];
        foreach ($priority as $p) if (in_array($p, $tables)) return $p;
        return array_shift($tables) ?? 'devices';
    }

    private function buildJoinClause(string $start_table, array $required_tables): array
    {
        $joins = [];
        $joined_tables = [$start_table => true];
        $aliased = $this->layer['aliased_tables'];
        $queue = new SplQueue();
        
        // Add aliased tables to joined_tables if their base is the start_table
        foreach ($aliased as $alias => $base) {
            if ($base === $start_table && in_array($alias, $required_tables)) {
                $joined_tables[$alias] = true;
            }
        }
        $queue->enqueue($start_table);

        $all_paths = $this->layer['join_paths'];

        // Create a full graph with reverse paths
        $graph = $all_paths;
        foreach ($all_paths as $left => $rights) {
            foreach ($rights as $right => $on) {
                if (!isset($graph[$right])) $graph[$right] = [];
                if (!isset($graph[$right][$left])) {
                    $reversed_on = implode(" = ", array_reverse(explode(" = ", $on)));
                    $graph[$right][$left] = $reversed_on;
                }
            }
        }

        $visited_for_pathfinding = [];
        $final_joins = [];
        $tables_to_join = array_diff($required_tables, [$start_table]);

        foreach ($tables_to_join as $target_table) {
            if (isset($joined_tables[$target_table])) continue;

            // BFS to find the shortest path
            $q = new SplQueue();
            $q->enqueue([$start_table]);
            $path_found = false;
            $paths = [[$start_table]];

            while (!$q->isEmpty() && !$path_found) {
                $current_path = array_shift($paths);
                $last_node = end($current_path);

                if ($last_node == $target_table) {
                    $path_found = true;
                    // Build joins from path
                    for ($i = 0; $i < count($current_path) - 1; $i++) {
                        $from = $current_path[$i];
                        $to = $current_path[$i+1];
                        if (!isset($joined_tables[$to])) {
                            $on_clause = $graph[$from][$to];
                            $table_real_name = $aliased[$to] ?? $to;
                            $final_joins[$to] = "LEFT JOIN {$table_real_name} AS {$to} ON {$on_clause}";
                            $joined_tables[$to] = true;
                        }
                    }
                } else {
                     if (isset($graph[$last_node])) {
                        foreach ($graph[$last_node] as $neighbor => $on) {
                            if (!in_array($neighbor, $current_path)) {
                                $new_path = $current_path;
                                $new_path[] = $neighbor;
                                $paths[] = $new_path;
                            }
                        }
                    }
                }
            }
        }

        // Final check
        foreach ($required_tables as $tbl) {
            if (!isset($joined_tables[$tbl])) {
                throw new Exception("امکان ساخت گزارش وجود ندارد. جدول مورد نیاز '{$tbl}' قابل اتصال نیست. لطفاً ابعاد و سنجه‌های انتخابی را بازبینی کنید.");
            }
        }
        return array_values($final_joins);
    }

    public function execute(PDO $pdo, array $definition): array
    {
        if (isset($definition['base_table']) || isset($definition['aggregations'])) {
            return [
                'columns' => [['key' => 'error', 'label' => 'خطای سازگاری']],
                'rows' => [[
                    'error' => 'این گزارش با فرمت قدیمی ذخیره شده و با سیستم جدید سازگار نیست. لطفاً آن را حذف کرده و در گزارش‌ساز بصری دوباره بسازید.'
                ]]
            ];
        }

        $query_data = $this->buildSql($definition);
        $stmt = $pdo->prepare($query_data['sql']);
        $stmt->execute($query_data['params']);
        $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $result_columns = [];
        $all_fields = array_merge($definition['rows'] ?? [], $definition['columns'] ?? []);
        foreach ($all_fields as $key) {
            if (!is_string($key)) continue;
            $field = $this->layer['dimensions'][$key] ?? $this->layer['measures'][$key] ?? null;
            if ($field) {
                $result_columns[] = ['key' => $key, 'label' => $field['label']];
            }
        }
        $limit = isset($definition['limit']) ? (int)$definition['limit'] : 1000;
        $warning = null;
        if (count($rows) >= $limit) {
            $warning = 'تعداد رکوردها به سقف محدودیت رسید. ممکن است داده‌ها کامل نباشد.';
        }
        return ['columns' => $result_columns, 'rows' => $rows, 'warning' => $warning];
    }
}
