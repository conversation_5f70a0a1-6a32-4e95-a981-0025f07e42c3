<?php
session_start();
require_once '../includes/auth.php';
require_once '../db_connection.php';

// بررسی مجوز دسترسی به صفحه تایید گزارش تعمیر
require_page_access('repair_approvals', 'view');

$pdo = db_connect();
require_once '../includes/date_helper.php';

$current_user_id = current_user_id();

// دریافت دستورکارهای منتظر تایید برای کاربر فعلی
$stmt = $pdo->prepare('
    SELECT 
        wo.id, wo.title, wo.workorder_id, wo.request_date, wo.status,
        wo.scheduled_activity_id,
        d.name AS device_name, d.serial_number,
        l.location_name,
        br.id AS breakdown_id,
        wo.stop_datetime,
        wo.line_stopped,
        exec.actual_start_datetime AS execution_start_time,
        exec.actual_end_datetime AS execution_end_time,
        exec.completion_notes AS execution_notes,
        (SELECT GROUP_CONCAT(u.name SEPARATOR "، ") FROM work_order_assignees woa JOIN users u ON woa.user_id = u.id WHERE woa.work_order_id = wo.id) AS assignees
    FROM work_orders wo
    JOIN devices d ON wo.device_id = d.id
    LEFT JOIN locations l ON d.location = l.id
    LEFT JOIN breakdown_reports br ON br.converted_to_wo_id = wo.id
    LEFT JOIN work_order_execution exec ON exec.work_order_id = wo.id
    WHERE wo.status = "منتظر تایید" AND wo.verifier_id = ?
    ORDER BY wo.request_date DESC
');
$stmt->execute([$current_user_id]);
$orders = $stmt->fetchAll(PDO::FETCH_ASSOC);


// هندل تایید
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['approve_id'])) {
    // بررسی مجوز تایید
    require_page_access('repair_approvals', 'approve');
    $wo_id = (int)$_POST['approve_id'];
    $restart_datetime = !empty($_POST['restart_datetime']) ? $_POST['restart_datetime'] : null;
    $now = date('Y-m-d H:i:s');

    // دریافت اطلاعات دستورکار و گزارش خرابی متصل
    $stmt = $pdo->prepare('SELECT wo.*, br.id AS breakdown_id, wo.stop_datetime, wo.line_stopped, wo.scheduled_activity_id FROM work_orders wo LEFT JOIN breakdown_reports br ON br.converted_to_wo_id = wo.id WHERE wo.id = ?');
    $stmt->execute([$wo_id]);
    $wo = $stmt->fetch(PDO::FETCH_ASSOC);
    if ($wo) {
        // بررسی ضروری بودن فیلدهای راه‌اندازی مجدد
        if ( ($wo['stop_datetime'] || $wo['line_stopped']) && !$restart_datetime) {
            $_SESSION['toast_message'] = 'برای دستورکارهایی که خط متوقف شده، اطلاعات راه‌اندازی مجدد الزامی است.';
            $_SESSION['toast_type'] = 'danger';
            header('Location: repair_approvals.php');
            exit;
        }
        
        $pdo->beginTransaction();
        try {
            // اگر توقف خط داشته و تاریخ راه‌اندازی مجدد وارد شده، ثبت کن
            if ( ($wo['stop_datetime'] || $wo['line_stopped']) && $restart_datetime) {
                $pdo->prepare('UPDATE work_orders SET restart_datetime = ? WHERE id = ?')->execute([$restart_datetime, $wo_id]);
            }
            // تغییر وضعیت دستورکار
            $pdo->prepare('UPDATE work_orders SET status = "انجام و تایید شده" WHERE id = ?')->execute([$wo_id]);
            // تغییر وضعیت گزارش خرابی متصل
            if ($wo['breakdown_id']) {
                $pdo->prepare('UPDATE breakdown_reports SET status = "انجام و تایید شده" WHERE id = ?')->execute([$wo['breakdown_id']]);
            }
            // اگر این دستورکار از یک فعالیت برنامه‌ریزی شده آمده بود، تاریخ آخرین سرویس را با زمان پایان واقعی تعمیر بروزرسانی کن
            if ($wo['scheduled_activity_id']) {
                // زمان پایان واقعی را از جدول work_order_execution بگیر
                $stmtEnd = $pdo->prepare('SELECT actual_end_datetime FROM work_order_execution WHERE work_order_id = ? ORDER BY id DESC LIMIT 1');
                $stmtEnd->execute([$wo_id]);
                $endRow = $stmtEnd->fetch(PDO::FETCH_ASSOC);
                $lastService = $endRow && $endRow['actual_end_datetime'] ? $endRow['actual_end_datetime'] : $now;
                $pdo->prepare('UPDATE activities SET last_activity_time = ? WHERE id = ?')->execute([$lastService, $wo['scheduled_activity_id']]);
            }
            $pdo->commit();
            $_SESSION['toast_message'] = 'دستورکار با موفقیت تایید شد.';
            $_SESSION['toast_type'] = 'success';
            header('Location: repair_approvals.php');
            exit;
        } catch (Exception $e) {
            $pdo->rollBack();
            $_SESSION['toast_message'] = 'خطا در تایید: ' . $e->getMessage();
            $_SESSION['toast_type'] = 'danger';
            header('Location: repair_approvals.php');
            exit;
        }
    }
}

// هندل رد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['reject_id'])) {
    // بررسی مجوز رد
    require_page_access('repair_approvals', 'reject');
    $wo_id = (int)$_POST['reject_id'];
    
    // دریافت اطلاعات دستورکار و گزارش خرابی متصل
    $stmt = $pdo->prepare('SELECT wo.*, br.id AS breakdown_id FROM work_orders wo LEFT JOIN breakdown_reports br ON br.converted_to_wo_id = wo.id WHERE wo.id = ?');
    $stmt->execute([$wo_id]);
    $wo = $stmt->fetch(PDO::FETCH_ASSOC);
    if ($wo) {
        $pdo->beginTransaction();
        try {
            // تغییر وضعیت دستورکار به "برگشت جهت اصلاح"
            $pdo->prepare('UPDATE work_orders SET status = "برگشت جهت اصلاح" WHERE id = ?')->execute([$wo_id]);
            // تغییر وضعیت گزارش خرابی متصل
            if ($wo['breakdown_id']) {
                $pdo->prepare('UPDATE breakdown_reports SET status = "برگشت جهت اصلاح" WHERE id = ?')->execute([$wo['breakdown_id']]);
            }
            $pdo->commit();
            $_SESSION['toast_message'] = 'دستورکار رد شد و برای اصلاح برگشت داده شد.';
            $_SESSION['toast_type'] = 'warning';
            header('Location: repair_approvals.php');
            exit;
        } catch (Exception $e) {
            $pdo->rollBack();
            $_SESSION['toast_message'] = 'خطا در رد: ' . $e->getMessage();
            $_SESSION['toast_type'] = 'danger';
            header('Location: repair_approvals.php');
            exit;
        }
    }
}

include '../includes/header.php';
?>
<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تایید گزارش‌های تعمیر</title>
    <?php 
    // دریافت پیام از session برای meta tags
    $toast_message = null;
    $toast_type = null;
    if (isset($_SESSION['toast_message'])) {
        $toast_message = $_SESSION['toast_message'];
        $toast_type = $_SESSION['toast_type'] ?? 'info';
        // حذف پیام از session تا فقط یک بار نمایش داده شود
        unset($_SESSION['toast_message']);
        unset($_SESSION['toast_type']);
    }
    ?>
    <?php if ($toast_message): ?>
        <meta name="toast-message" content="<?= htmlspecialchars($toast_message) ?>">
        <meta name="toast-type" content="<?= htmlspecialchars($toast_type) ?>">
    <?php endif; ?>
    <style>
        :root {
            --main-color: #d94307;
            --background-color: #f4f7f6;
            --card-bg: #fff;
            --text-color: #333;
            --text-muted: #6c757d;
            --border-color: #e9ecef;
            --success-color: #28a745;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            margin: 0;
            background-color: var(--background-color);
            color: var(--text-color);
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 1.5rem 1rem;
        }
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2.5rem;
        }
        .page-title {
            color: var(--main-color);
            margin: 0;
            font-size: var(--fs-2xl);
        }
        .approval-cards-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 1.5rem;
        }
        .approval-card {
            background-color: var(--card-bg);
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.07);
            border: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        .approval-card__header {
            padding: 1rem 1.25rem;
            background-color: #f8f9fa;
            border-bottom: 1px solid var(--border-color);
        }
        .approval-card__title {
            font-size: var(--fs-xl);
            font-weight: 700;
            margin: 0 0 0.25rem 0;
            color: var(--main-color);
        }
        .approval-card__wo-id {
            font-size: var(--fs-sm);
            color: var(--text-muted);
        }
        .approval-card__body {
            padding: 1.25rem;
            flex-grow: 1;
        }
        .detail-row {
            display: grid;
            align-items: flex-start;
            margin-bottom: 1rem;
            font-size: var(--fs-sm);
        }
        .detail-row i {
            margin-left: 0.75rem;
            color: var(--text-muted);
            width: 20px;
            text-align: center;
        }
        .detail-value {
            font-weight: 500;
        }
        .detail-value.full-width {
            width: 100%;
        }
        .detail-description {
            margin-top: 0.25rem;
            padding-right: 2rem;
            line-height: 1.7;
            color: #555;
            white-space: pre-wrap;
            background-color: #fbfbfb;
            padding: 0.5rem 1rem 0.5rem 2rem;
            border-radius: 6px;
        }
        .approval-card__footer {
            padding: 1rem 1.25rem;
            background-color: #f8f9fa;
            border-top: 1px solid var(--border-color);
        }
        .approval-form {
            display: flex;
            flex-wrap: wrap;
            gap: 0.75rem;
            align-items: center;
            justify-content: space-between;
        }
        .restart-datetime-input {
            padding: 0.5rem;
            border: 1px solid #ccc;
            border-radius: 4px;
            flex-grow: 1;
        }
        .btn-approve {
            padding: 0.6rem 1.5rem;
            border-radius: 6px;
            background-color: var(--success-color);
            color: #fff;
            border: none;
            cursor: pointer;
            font-weight: 600;
        }
        .btn-approve:hover { opacity: 0.9; }
        .btn-reject {
            padding: 0.6rem 1.5rem;
            border-radius: 6px;
            background-color: #dc3545;
            color: #fff;
            border: none;
            cursor: pointer;
            font-weight: 600;
            margin-right: 0.5rem;
        }
        .btn-reject:hover { opacity: 0.9; }
        .no-items-message { text-align: center; color: var(--text-muted); padding: 3rem; }
        .detail-label {
            font-weight: 600;
            min-width: 160px;
            display: inline-block;
            color: #444;
        }
        .detail-row {
            margin-bottom: 0.7rem;
            display: grid;
            align-items: flex-start;
            gap: 0.5rem;
        }
        input.restart-hour-input, input.restart-minute-input {
        min-width: 64px;
        text-align: center;
        }
        
                
        .required-asterisk {
            color: #dc3545;
            font-weight: bold;
        }
        
        
        .restart-section label {
            color: #856404;
            font-weight: 600;
        }
        .status-badge, .status-done-confirmed {
            padding: 0.2em 0.6em;
            border-radius: 0.25rem;
            font-size: var(--fs-xs);
            font-weight: bold;
            color: white;
            flex-shrink: 0;
        }
        .status-done-confirmed { background-color: #28a745 !important; color: #fff !important; }
        
        .alert {
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 6px;
            border: 1px solid transparent;
        }
        .alert-success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .alert-warning {
            background-color: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        .alert-danger {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        
        /* Toast Message Styles */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
        }
        .toast {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            padding: 1rem 1.5rem;
            margin-bottom: 10px;
            border-right: 4px solid;
            min-width: 300px;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            opacity: 0;
        }
        .toast.show {
            transform: translateX(0);
            opacity: 1;
        }
        .toast-success {
            border-color: #28a745;
            color: #155724;
        }
        .toast-warning {
            border-color: #ffc107;
            color: #856404;
        }
        .toast-danger {
            border-color: #dc3545;
            color: #721c24;
        }
        .toast-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        .toast-title {
            font-weight: 600;
            font-size: var(--fs-sm);
        }
        .toast-close {
            background: none;
            border: none;
            font-size: 1.2rem;
            cursor: pointer;
            color: #666;
            padding: 0;
            line-height: 1;
        }
        .toast-message {
            font-size: var(--fs-sm);
            line-height: 1.4;
        }
    </style>
</head>
<body>
<div id="toast-container" class="toast-container"></div>
<div class="container">
    <div class="page-header">
        <h2 class="page-title"><i class="fas fa-check-double"></i> تایید گزارش‌های تعمیر</h2>
    </div>



    <?php if (empty($orders)): ?>
        <div class="no-items-message">در حال حاضر دستورکاری برای تایید شما وجود ندارد.</div>
    <?php else: ?>
        <div class="approval-cards-container">
            <?php foreach ($orders as $order): ?>
                <div class="approval-card" data-has-line-stop="<?= (!empty($order['stop_datetime']) || !empty($order['line_stopped'])) ? 'true' : 'false' ?>">
                    <div class="approval-card__header">
                        <h3 class="approval-card__title">عنوان دستورکار: <?= htmlspecialchars($order['title']) ?></h3>
                        <span class="approval-card__wo-id">
                            شماره دستورکار: <?= htmlspecialchars($order['workorder_id']) ?>
                            <?php if($order['breakdown_id']): ?>
                                | شماره گزارش خرابی: <?= htmlspecialchars($order['breakdown_id']) ?>
                            <?php endif; ?>
                        </span>
                    </div>
                    <div class="approval-card__body">
                        <div class="detail-row">
                            <span class="detail-label">دستگاه / محل / سریال:</span>
                            <span class="detail-value"><?= htmlspecialchars($order['device_name']) ?> | <?= htmlspecialchars($order['location_name'] ?? '-') ?> | <?= htmlspecialchars($order['serial_number'] ?? '-') ?></span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">سرپرست تعمیر:</span>
                            <span class="detail-value"><?= htmlspecialchars($order['assignees'] ?? 'مشخص نشده') ?></span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">شروع کار:</span>
                            <span class="detail-value"><?= $order['execution_start_time'] ? format_shamsi_datetime($order['execution_start_time']) : '-' ?></span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">پایان کار:</span>
                            <span class="detail-value"><?= $order['execution_end_time'] ? format_shamsi_datetime($order['execution_end_time']) : '-' ?></span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">شرح اقدامات انجام شده:</span>
                            <span class="detail-value full-width"><p class="detail-description"><?= htmlspecialchars($order['execution_notes'] ?? 'ثبت نشده') ?></p></span>
                        </div>
                    </div>
                    <div class="approval-card__footer">
                        <form method="post" class="approval-form">
                            <input type="hidden" name="approve_id" value="<?= $order['id'] ?>">
                            <div class="restart-section" style="display: none; width:100%;">
                                <div class="row" style="width:100%;margin-bottom:0.5rem;">
                                    <div class="col" style="display:inline-block;width:50%;">
                                        <label for="restart_date_<?= $order['id'] ?>">تاریخ راه‌اندازی مجدد:</label>
                                        <input type="text" id="restart_date_<?= $order['id'] ?>" name="restart_date" class="restart-date-input persian-datepicker" placeholder="تاریخ راه‌اندازی مجدد" autocomplete="off">
                                    </div>
                                    <div class="col" style="display:inline-block;width:48%;">
                                    <input type="text" id="restart_minute_<?= $order['id'] ?>" name="restart_minute" class="restart-minute-input" placeholder="دقیقه" maxlength="2" style="width:40px;display:inline-block;"> :
                                        <input type="text" id="restart_hour_<?= $order['id'] ?>" name="restart_hour" class="restart-hour-input" placeholder="ساعت" maxlength="2" style="width:40px;display:inline-block;"> 
                                        <input type="hidden" name="restart_datetime" class="restart-datetime-hidden">
                                    </div>
                                </div>
                            </div>
                            <span style="margin-left:1rem; font-weight:600;">تحویل گیرنده: <?= htmlspecialchars(current_user_name()) ?></span>
                            <div style="display: flex; gap: 0.5rem;">
                                <?php if (has_permission('repair_approvals', 'approve')): ?>
                                <button type="submit" class="btn-approve">تایید نهایی</button>
                                <?php endif; ?>
                                <?php if (has_permission('repair_approvals', 'reject')): ?>
                                <button type="button" class="btn-reject" onclick="rejectWorkOrder(<?= $order['id'] ?>)">رد</button>
                                <?php endif; ?>
                                <?php if (!has_permission('repair_approvals', 'approve') && !has_permission('repair_approvals', 'reject')): ?>
                                <div class="alert alert-info">شما مجوز تایید یا رد گزارش‌ها را ندارید.</div>
                                <?php endif; ?>
                            </div>
                        </form>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
</div>

<?php include '../includes/footer.php'; ?>
<script>
function rejectWorkOrder(workOrderId) {
    if (confirm('آیا از رد کردن این دستورکار اطمینان دارید؟')) {
        var form = document.createElement('form');
        form.method = 'POST';
        form.action = '';
        
        var input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'reject_id';
        input.value = workOrderId;
        
        form.appendChild(input);
        document.body.appendChild(form);
        form.submit();
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // راه‌اندازی تقویم با روش استاندارد پروژه
    initializeDatepicker('.persian-datepicker');

    // انتقال خودکار فوکوس از ساعت به دقیقه پس از وارد کردن دو رقم
    $(document).on('keyup', '.restart-hour-input', function() {
        if (this.value.length >= 2) {
            $(this).closest('.restart-section').find('.restart-minute-input').focus();
        }
    });

    // منطق اصلی برای نمایش/پنهان‌سازی و الزامی کردن فیلدها
    $('.approval-card').each(function() {
        const card = $(this);
        const hasLineStop = card.data('has-line-stop') === true;
        const restartSection = card.find('.restart-section');
        
        if (hasLineStop) {
            restartSection.show();
            // الزامی کردن فیلدهای ورودی
            restartSection.find('input').prop('required', true);
            // اطمینان از اینکه فیلد مخفی الزامی نیست
            restartSection.find('.restart-datetime-hidden').prop('required', false);
        }
    });

    // اعتبارسنجی هنگام ارسال فرم
    $('.approval-form').on('submit', function(e) {
        const form = this;
        const card = $(form).closest('.approval-card');
        const hasLineStop = card.data('has-line-stop') === true;

        if (hasLineStop) {
            const dateInput = form.querySelector('.restart-date-input');
            const hourInput = form.querySelector('.restart-hour-input');
            const minuteInput = form.querySelector('.restart-minute-input');
            const datetimeHidden = form.querySelector('.restart-datetime-hidden');

            if (!dateInput.value || !hourInput.value || !minuteInput.value) {
                alert('لطفاً تاریخ و زمان کامل راه‌اندازی مجدد خط را وارد کنید.');
                e.preventDefault(); // جلوگیری از ارسال فرم
                return false;
            }

            // ترکیب تاریخ و زمان و قرار دادن در فیلد مخفی
            let hour = hourInput.value.padStart(2, '0');
            let minute = minuteInput.value.padStart(2, '0');
            datetimeHidden.value = `${dateInput.value} ${hour}:${minute}`;
        }
    });
    
    // فراخوانی تابع برای علامت‌گذاری فیلدهای الزامی
    if (typeof markRequiredFields === 'function') {
        markRequiredFields();
    }
});
</script>
</body>
</html> 