-- تحديث قاعدة البيانات لتنظيم الصفحات الإدارية
-- Database update to organize administrative pages

-- إضافة صفحة إدارة المستخدمين إذا لم تكن موجودة
-- Add user management page if it doesn't exist
INSERT INTO system_pages (page_name, display_name, description, file_path, icon, sort_order, is_active)
VALUES ('user_management', 'مدیریت کاربران', 'مدیریت کاربران و نقش‌های سیستم', 'user_management.php', 'fas fa-users-cog', 11, 1)
ON DUPLICATE KEY UPDATE 
    display_name = VALUES(display_name), 
    description = VALUES(description), 
    file_path = VALUES(file_path), 
    icon = VALUES(icon), 
    sort_order = VALUES(sort_order);

-- إضافة الأذونات لصفحة إدارة المستخدمين
-- Add permissions for user management page
INSERT INTO permissions (page_id, permission_name, display_name, description)
VALUES
    ((SELECT id FROM system_pages WHERE page_name = 'user_management'), 'view', 'مشاهده مدیریت کاربران', 'دسترسی به صفحه مدیریت کاربران'),
    ((SELECT id FROM system_pages WHERE page_name = 'user_management'), 'create', 'ایجاد کاربر', 'افزودن کاربر جدید'),
    ((SELECT id FROM system_pages WHERE page_name = 'user_management'), 'edit', 'ویرایش کاربر', 'ویرایش اطلاعات کاربران'),
    ((SELECT id FROM system_pages WHERE page_name = 'user_management'), 'delete', 'حذف کاربر', 'حذف کاربران'),
    ((SELECT id FROM system_pages WHERE page_name = 'user_management'), 'manage_roles', 'مدیریت نقش‌ها', 'ایجاد و ویرایش نقش‌ها'),
    ((SELECT id FROM system_pages WHERE page_name = 'user_management'), 'view_all_profiles', 'مشاهده همه پروفایل‌ها', 'دسترسی به مشاهده پروفایل همه کاربران')
ON DUPLICATE KEY UPDATE 
    display_name = VALUES(display_name), 
    description = VALUES(description);

-- تأكد من تخصيص جميع الأذونات لدور المدير
-- Ensure all permissions are assigned to admin role
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r
CROSS JOIN permissions p
WHERE r.name = 'admin'
ON DUPLICATE KEY UPDATE granted_at = VALUES(granted_at);

-- تحديث ترتيب الصفحات لتنظيم أفضل
-- Update page sort order for better organization
UPDATE system_pages SET sort_order = 1 WHERE page_name = 'dashboard';
UPDATE system_pages SET sort_order = 2 WHERE page_name = 'devices';
UPDATE system_pages SET sort_order = 3 WHERE page_name = 'activities';
UPDATE system_pages SET sort_order = 4 WHERE page_name = 'work_order';
UPDATE system_pages SET sort_order = 5 WHERE page_name = 'reports_list';
UPDATE system_pages SET sort_order = 6 WHERE page_name = 'my_tasks';
UPDATE system_pages SET sort_order = 7 WHERE page_name = 'profile';
UPDATE system_pages SET sort_order = 8 WHERE page_name = 'reminders';
UPDATE system_pages SET sort_order = 9 WHERE page_name = 'report_builder';
UPDATE system_pages SET sort_order = 10 WHERE page_name = 'dashboard_reports';
UPDATE system_pages SET sort_order = 11 WHERE page_name = 'user_management';
