<?php
require_once '../includes/auth.php';
require_once '../db_connection.php';
require_once '../includes/date_helper.php';

// بررسی دسترسی به صفحه گزارش‌های خرابی
require_page_access('reports_list', 'view');

// تنظیم منطقه زمانی برای تمام محاسبات تاریخ
date_default_timezone_set('Asia/Tehran');

$pdo = db_connect();

/**
 * تابع کمکی برای تبدیل اعداد انگلیسی به فارسی
 * @param string $string رشته حاوی اعداد انگلیسی
 * @return string رشته با اعداد فارسی
 */
function to_persian_numerals($string) {
    $persian_digits = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
    $english_digits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    return str_replace($english_digits, $persian_digits, $string);
}

function json_response($data, $statusCode = 200) {
    http_response_code($statusCode);
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}

// ## بخش جدید: مدیریت درخواست AJAX برای سوابق ##
if (isset($_GET['action'])) {
    if ($_GET['action'] === 'get_report_history') {
        $search_term = $_GET['search'] ?? '';
        $from_date = !empty($_GET['from_date']) ? to_miladi($_GET['from_date']) : null;
        $to_date = !empty($_GET['to_date']) ? to_miladi($_GET['to_date']) : null;
        $status = $_GET['status'] ?? 'all'; // دریافت فیلتر وضعیت

        // **MODIFIED**: Added d.serial_number and d.location to the query
        $sql = "SELECT 
                    br.id, br.problem_description, br.report_datetime, br.status, br.converted_to_wo_id,
                    d.name as device_name,
                    d.serial_number,
                    d.location,
                    l.location_name,
                    u.name as reporter_name
                FROM breakdown_reports br
                JOIN devices d ON br.device_id = d.id
                LEFT JOIN locations l ON d.location = l.id
                JOIN users u ON br.reported_by_id = u.id
                WHERE 1=1";
        
        $params = [];

        if ($from_date) {
            $sql .= " AND DATE(br.report_datetime) >= ?";
            $params[] = $from_date;
        }
        if ($to_date) {
            $sql .= " AND DATE(br.report_datetime) <= ?";
            $params[] = $to_date;
        }
        if (!empty($search_term)) {
            $sql .= " AND (d.name LIKE ? OR br.problem_description LIKE ? OR d.serial_number LIKE ? OR d.location LIKE ?)";
            $params[] = "%$search_term%";
            $params[] = "%$search_term%";
            $params[] = "%$search_term%";
            $params[] = "%$search_term%";
        }
        // اعمال فیلتر وضعیت
        if ($status !== 'all' && !empty($status)) {
            $sql .= " AND br.status = ?";
            $params[] = $status;
        }

        $sql .= " ORDER BY br.report_datetime DESC";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $history = $stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($history as &$item) {
            $item['report_datetime_shamsi'] = format_shamsi_datetime($item['report_datetime']);
            // مقدار location_name را اگر null بود، به - تبدیل کن
            if (!isset($item['location_name']) || $item['location_name'] === null) {
                $item['location_name'] = '-';
            }
            if ($item['converted_to_wo_id']) {
                $wo_stmt = $pdo->prepare("SELECT workorder_id FROM work_orders WHERE id = ?");
                $wo_stmt->execute([$item['converted_to_wo_id']]);
                $wo_info = $wo_stmt->fetch(PDO::FETCH_ASSOC);
                $item['workorder_number'] = $wo_info ? $wo_info['workorder_id'] : null;
            }
        }

        json_response(['success' => true, 'history' => $history]);
    }
    // اکشن برای دریافت جزئیات دستور کار
    if ($_GET['action'] === 'get_work_order_details') {
        $id = $_GET['id'] ?? 0;
        if (!$id) {
            json_response(['success' => false, 'message' => 'شناسه نامعتبر است.'], 400);
        }
        $stmt_wo = $pdo->prepare("
            SELECT wo.*, d.name as device_name, u.name as requester_name
            FROM work_orders wo
            LEFT JOIN devices d ON wo.device_id = d.id
            LEFT JOIN users u ON wo.requester_id = u.id
            WHERE wo.id = ?
        ");
        $stmt_wo->execute([$id]);
        $work_order = $stmt_wo->fetch(PDO::FETCH_ASSOC);

        if (!$work_order) {
            json_response(['success' => false, 'message' => 'دستور کار یافت نشد.'], 404);
        }
        $stmt_assignees = $pdo->prepare("SELECT u.name FROM users u JOIN work_order_assignees woa ON u.id = woa.user_id WHERE woa.work_order_id = ?");
        $stmt_assignees->execute([$id]);
        $assignees = $stmt_assignees->fetchAll(PDO::FETCH_ASSOC);

        $work_order['request_date_shamsi'] = format_shamsi_datetime($work_order['request_date']);
        $work_order['due_date_shamsi'] = $work_order['due_date'] ? format_shamsi_datetime($work_order['due_date']) : '-';


        json_response(['success' => true, 'work_order' => $work_order, 'assignees' => $assignees]);
    }
    // اکشن برای دریافت لاگ‌های poke
    if ($_GET['action'] === 'get_poke_logs') {
        $report_id = $_GET['report_id'] ?? 0;
        if (!$report_id) {
            json_response(['success' => false, 'message' => 'شناسه گزارش نامعتبر است.'], 400);
        }

        $logs_stmt = $pdo->prepare("
            SELECT
                brp.*,
                sender.name as sender_name,
                recipient.name as recipient_name
            FROM breakdown_report_pokes brp
            JOIN users sender ON brp.sender_id = sender.id
            JOIN users recipient ON brp.recipient_id = recipient.id
            WHERE brp.report_id = ?
            ORDER BY brp.created_at DESC
        ");
        $logs_stmt->execute([$report_id]);
        $logs = $logs_stmt->fetchAll(PDO::FETCH_ASSOC);

        // تبدیل تاریخ‌ها به شمسی
        foreach ($logs as &$log) {
            $log['created_at_shamsi'] = format_shamsi_datetime($log['created_at']);
        }

        json_response(['success' => true, 'logs' => $logs]);
    }
}

// مدیریت درخواست POST برای ارسال poke
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['report_id']) && isset($_POST['message'])) {
    $report_id = (int)$_POST['report_id'];
    $message = trim($_POST['message']);
    $sender_id = current_user_id();

    if (empty($message)) {
        json_response(['success' => false, 'message' => 'پیام نمی‌تواند خالی باشد.'], 400);
    }

    try {
        // دریافت اطلاعات گزارش خرابی
        $report_stmt = $pdo->prepare("
            SELECT br.*, d.name as device_name
            FROM breakdown_reports br
            JOIN devices d ON br.device_id = d.id
            WHERE br.id = ?
        ");
        $report_stmt->execute([$report_id]);
        $report = $report_stmt->fetch(PDO::FETCH_ASSOC);

        if (!$report) {
            json_response(['success' => false, 'message' => 'گزارش خرابی یافت نشد.'], 404);
        }

        // تعیین گیرندگان پیام
        $recipients = [];

        // اگر گزارش به دستورکار تبدیل شده
        if ($report['converted_to_wo_id']) {
            // 1. صادرکننده دستورکار (requester)
            $requester_stmt = $pdo->prepare("SELECT requester_id FROM work_orders WHERE id = ?");
            $requester_stmt->execute([$report['converted_to_wo_id']]);
            $requester = $requester_stmt->fetch(PDO::FETCH_ASSOC);
            if ($requester && $requester['requester_id']) {
                $recipients[] = $requester['requester_id'];
            }

            // 2. مسئولین اجرای دستورکار (assignees)
            $assignees_stmt = $pdo->prepare("
                SELECT DISTINCT user_id
                FROM work_order_assignees
                WHERE work_order_id = ?
            ");
            $assignees_stmt->execute([$report['converted_to_wo_id']]);
            $assignees = $assignees_stmt->fetchAll(PDO::FETCH_COLUMN);
            $recipients = array_merge($recipients, $assignees);
        } else {
            // اگر هنوز به دستورکار تبدیل نشده، به کارشناس نت ارسال شود
            $admin_stmt = $pdo->prepare("SELECT id FROM users WHERE role = 'admin' OR role_id = (SELECT id FROM roles WHERE name = 'admin') LIMIT 1");
            $admin_stmt->execute();
            $admin = $admin_stmt->fetch(PDO::FETCH_ASSOC);
            if ($admin) {
                $recipients[] = $admin['id'];
            }
        }

        // حذف تکراری‌ها و فرستنده
        $recipients = array_unique($recipients);
        $recipients = array_filter($recipients, function($id) use ($sender_id) {
            return $id != $sender_id;
        });

        if (empty($recipients)) {
            json_response(['success' => false, 'message' => 'هیچ گیرنده‌ای برای ارسال پیام یافت نشد.'], 400);
        }

        // ثبت پیام‌های poke
        $poke_stmt = $pdo->prepare("
            INSERT INTO breakdown_report_pokes (report_id, sender_id, recipient_id, message)
            VALUES (?, ?, ?, ?)
        ");

        $pdo->beginTransaction();

        foreach ($recipients as $recipient_id) {
            $poke_stmt->execute([$report_id, $sender_id, $recipient_id, $message]);
        }

        $pdo->commit();

        json_response(['success' => true, 'message' => 'یادآوری با موفقیت ارسال شد.']);

    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        error_log("Poke submission failed: " . $e->getMessage());
        json_response(['success' => false, 'message' => 'خطا در ارسال یادآوری.'], 500);
    }
}

// اکشن برای دریافت لیست گیرندگان پیام
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['action']) && $_GET['action'] === 'get_recipients') {
    $report_id = $_GET['report_id'] ?? 0;
    if (!$report_id) {
        json_response(['success' => false, 'message' => 'شناسه گزارش نامعتبر است.'], 400);
    }

    try {
        // دریافت اطلاعات گزارش خرابی
        $report_stmt = $pdo->prepare("
            SELECT br.*, d.name as device_name
            FROM breakdown_reports br
            JOIN devices d ON br.device_id = d.id
            WHERE br.id = ?
        ");
        $report_stmt->execute([$report_id]);
        $report = $report_stmt->fetch(PDO::FETCH_ASSOC);

        if (!$report) {
            json_response(['success' => false, 'message' => 'گزارش خرابی یافت نشد.'], 404);
        }


        // منطق جدید: ادغام نقش‌ها برای افراد تکراری
        $recipient_roles = [];

        if ($report['converted_to_wo_id']) {
            // صادرکننده دستورکار
            $requester_stmt = $pdo->prepare("
                SELECT wo.requester_id, u.name as requester_name
                FROM work_orders wo
                JOIN users u ON wo.requester_id = u.id
                WHERE wo.id = ?
            ");
            $requester_stmt->execute([$report['converted_to_wo_id']]);
            $requester = $requester_stmt->fetch(PDO::FETCH_ASSOC);
            if ($requester && $requester['requester_id']) {
                $recipient_roles[$requester['requester_id']] = ['name' => $requester['requester_name'], 'roles' => ['صادرکننده دستورکار']];
            }

            // مسئولین اجرا
            $assignees_stmt = $pdo->prepare("
                SELECT DISTINCT woa.user_id, u.name
                FROM work_order_assignees woa
                JOIN users u ON woa.user_id = u.id
                WHERE woa.work_order_id = ?
            ");
            $assignees_stmt->execute([$report['converted_to_wo_id']]);
            $assignees = $assignees_stmt->fetchAll(PDO::FETCH_ASSOC);
            foreach ($assignees as $assignee) {
                if (isset($recipient_roles[$assignee['user_id']])) {
                    if (!in_array('مسئول اجرا', $recipient_roles[$assignee['user_id']]['roles'])) {
                        $recipient_roles[$assignee['user_id']]['roles'][] = 'مسئول اجرا';
                    }
                } else {
                    $recipient_roles[$assignee['user_id']] = ['name' => $assignee['name'], 'roles' => ['مسئول اجرا']];
                }
            }
        } else {
            // اگر هنوز به دستورکار تبدیل نشده، به کارشناس نت ارسال شود
            $admin_stmt = $pdo->prepare("
                SELECT id, name FROM users
                WHERE role = 'admin' OR role_id = (SELECT id FROM roles WHERE name = 'admin')
                LIMIT 1
            ");
            $admin_stmt->execute();
            $admin = $admin_stmt->fetch(PDO::FETCH_ASSOC);
            if ($admin) {
                $recipient_roles[$admin['id']] = ['name' => $admin['name'], 'roles' => ['کارشناس شبکه']];
            }
        }

        // حذف فرستنده از لیست گیرندگان
        $sender_id = current_user_id();
        unset($recipient_roles[$sender_id]);

        // ساخت خروجی نهایی
        $recipients_out = [];
        foreach ($recipient_roles as $info) {
            $roles_str = implode('، ', $info['roles']);
            $recipients_out[] = $info['name'] . ' (' . $roles_str . ')';
        }

        json_response([
            'success' => true,
            'recipients' => $recipients_out,
            'count' => count($recipients_out)
        ]);

    } catch (Exception $e) {
        error_log("Get recipients failed: " . $e->getMessage());
        json_response(['success' => false, 'message' => 'خطا در دریافت لیست گیرندگان.'], 500);
    }
}

// فقط گزارش‌های جدید (وضعیت گزارش شده) نمایش داده شوند
$main_query_condition = " WHERE br.status = 'گزارش شده'";

$highlight_id = null;
if (isset($_GET['highlight']) && is_numeric($_GET['highlight'])) {
    $highlight_id = (int)$_GET['highlight'];
    // اگر highlight شده، همه گزارش‌ها را نمایش بده
    $main_query_condition = "";
}

// تعیین فیلتر بر اساس مجوزهای کاربر
$current_user_id = current_user_id();
$permission_condition = "";

if (has_permission('reports_list', 'view_all')) {
    // کاربر می‌تواند همه گزارش‌ها را ببیند
    $permission_condition = "";
} elseif (has_permission('reports_list', 'view_own')) {
    // کاربر فقط می‌تواند گزارش‌های خود را ببیند
    if ($main_query_condition) {
        $permission_condition = " AND br.reported_by_id = $current_user_id";
    } else {
        $permission_condition = " WHERE br.reported_by_id = $current_user_id";
    }
} else {
    // کاربر هیچ گزارشی نمی‌تواند ببیند
    $permission_condition = " WHERE 1=0";
}

$main_query_condition .= $permission_condition;

// **MODIFIED**: Added d.serial_number and d.location to the main query
$stmt = $pdo->query("
    SELECT 
        br.id, br.problem_description, br.urgency, br.report_datetime,
        br.breakdown_datetime, br.line_stoppage_datetime,
        d.name AS device_name,
        d.serial_number,
        d.location,
        l.location_name,
        u.name AS reporter_name,
        (SELECT GROUP_CONCAT(image_path) FROM breakdown_report_images WHERE report_id = br.id) AS images
    FROM breakdown_reports br
    JOIN devices d ON br.device_id = d.id
    LEFT JOIN locations l ON d.location = l.id
    JOIN users u ON br.reported_by_id = u.id
    {$main_query_condition}
    ORDER BY FIELD(br.urgency, 'فوری', 'عادی'), br.report_datetime DESC
");
$reports = $stmt->fetchAll(PDO::FETCH_ASSOC);

include '../includes/header.php';
?>
<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لیست گزارش‌های خرابی</title>
    <style>
        :root {
            --main-color: #d94307;
            --background-color: #f4f7f6;
            --card-bg: #fff;
            --text-color: #333;
            --text-muted: #6c757d;
            --border-color: #e9ecef;
            --fs-2xl: 1.5rem;
            --fs-3xl: 1.75rem;
            --fs-xl: 1.2rem;
            --fs-sm: 0.85rem;
            --fs-xs: 0.8rem;
        }

        *, *::before, *::after {
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            margin: 0;
            background-color: var(--background-color);
            color: var(--text-color);
            line-height: 1.6;
        }

        #page-reports-list .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem 1rem;
        }

        .page-header {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            align-items: center;
            gap: 1rem;
            margin-bottom: 2.5rem;
        }
        .header-actions {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            gap: 1rem;
        }


        #page-reports-list .page-title {
            color: var(--main-color);
            margin: 0;
            font-size: var(--fs-2xl);
        }
        @media (min-width: 768px) {
            #page-reports-list .page-title {
                font-size: var(--fs-3xl);
            }
        }

        .report-cards-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 1.5rem;
        }

        .report-card-container {
            perspective: 1000px;
            min-height: 320px;
        }
        .report-card-container.highlighted {
            border: 2px solid #ffc107;
            border-radius: 14px;
            box-shadow: 0 0 15px rgba(255, 193, 7, 0.7);
        }

        .report-card__inner {
            position: relative;
            width: 100%;
            height: 100%;
            transition: transform 0.6s;
            transform-style: preserve-3d;
        }
        .report-card__inner.is-flipped {
            transform: rotateY(180deg);
        }

        .report-card__front, .report-card__back {
            position: absolute;
            width: 100%;
            height: 100%;
            -webkit-backface-visibility: hidden;
            backface-visibility: hidden;
            display: flex;
            flex-direction: column;
            background-color: var(--card-bg);
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.07);
            border: 1px solid var(--border-color);
        }
        .report-card__back {
            transform: rotateY(180deg);
        }
        
        .report-card__front.urgent-border, .report-card__back.urgent-border {
            border-top: 5px solid #e53e3e;
        }

        .report-card__header {
            padding: 1rem 1.25rem;
            display: flex;
            justify-content: space-between;
            align-items: flex-start; /* Changed for better alignment */
            border-bottom: 1px solid var(--border-color);
            flex-shrink: 0;
        }
        /* **ADDED**: New styles for device info block */
        .report-card__device-info {
            flex-grow: 1;
        }
        .report-card__device-name {
            font-size: 1.2rem;
            font-weight: 700;
            display: block; /* Make it a block for spacing */
        }
        /* **ADDED**: New styles for location */
        .report-card__location {
            font-size: var(--fs-sm);
            color: var(--text-muted);
            display: block;
            margin-top: 4px;
        }
        /* **ADDED**: New styles for header right section */
        .report-card__header-right {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex-shrink: 0;
        }
        .report-card__urgency {
            padding: 0.3rem 0.8rem;
            border-radius: 999px;
            font-size: var(--fs-xs);
            font-weight: 600;
        }
        .report-card__urgency.urgent {
            background-color: #fed7d7;
            color: #c53030;
        }
        /* **ADDED**: New styles for reminder button */
        .btn-reminder {
            background: none;
            border: none;
            color: #6c757d;
            font-size: 1.1rem;
            cursor: pointer;
            padding: 0.25rem;
            border-radius: 4px;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
        }
        .btn-reminder:hover {
            color:  var(--warning-color);
        }
        .btn-reminder:active {
            transform: scale(0.95);
        }

        .report-card__body {
            padding: 1.25rem;
            flex-grow: 1;
            overflow-y: auto;
        }
        .report-card__description {
            color: var(--text-muted);
            line-height: 1.7;
            margin-top: 0;
        }
        .report-card__info {
            margin-top: 1rem;
            font-size: var(--fs-sm);
            color: var(--text-muted);
        }
        .report-card__info span {
            display: block;
            margin-bottom: 0.5rem;
        }
        .line-stopped-indicator {
            color: #c53030;
            font-weight: 600;
        }

        .report-card__footer {
            padding: 1rem 1.25rem;
            background-color: #f8f9fa;
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-radius: 0 0 12px 12px;
            flex-shrink: 0;
        }

        .btn, .btn-details, .btn-details-back, .btn-convert {
            padding: 0.6rem 1.2rem;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 600;
            border: none;
            cursor: pointer;
            font-family: inherit;
            transition: background-color 0.2s, opacity 0.2s;
        }
        .btn-details, .btn-details-back {
            background-color: #e9ecef;
            color: #495057;
            border: 1px solid #dee2e6;
        }
        .btn-details:hover, .btn-details-back:hover {
            background-color: #dee2e6;
        }
        .btn-convert {
            background-color: #28a745;
            color: #fff;
        }
        .btn-convert:hover {
            opacity: 0.9;
        }
        .btn-poke-logs {
            background-color: #17a2b8;
            color: white;
            border: 1px solid #17a2b8;
            padding: 0.4rem 0.8rem;
            font-size: 0.85rem;
        }
        .btn-poke-logs:hover {
            background-color: #138496;
            border-color: #117a8b;
        }
        .back-header-buttons {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }
        .btn-secondary {
            background-color: #2563eb;
            color: white;
        }

        .report-card__body-back {
            padding: 1.25rem;
            overflow-y: auto;
            flex-grow: 1;
        }
        .report-card__body-back p {
            margin: 0 0 1rem 0;
        }
        .image-gallery img {
            width: 100%;
            height: auto;
            max-height: 200px;
            object-fit: cover;
            border-radius: 8px;
            border: 1px solid #ddd;
            cursor: pointer;
            display: block;
            margin-bottom: 10px;
            transition: transform .2s, box-shadow .2s;
        }
        .image-gallery img:hover {
            transform: scale(1.03);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .no-reports {
            text-align: center;
            padding: 3rem;
            font-size: var(--fs-xl);
            color: var(--text-muted);
        }

        /* استایل‌های مودال‌ها */
        .modal {
            display: none;
            position: fixed;
            z-index: 10000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0,0,0,0.6);
            animation: fadeIn 0.3s;
        }
        .modal-content {
            background-color: #fefefe;
            margin: 5% auto;
            padding: 20px;
            border: 1px solid #888;
            width: 90%;
            max-width: 800px;
            border-radius: 8px;
        }
        .close-modal-btn {
            color: #aaa;
            float: left;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        /* استایل‌های مودال poke */
        .predefined-messages {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }
        .predefined-msg-btn {
            padding: 0.75rem;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            text-align: right;
            cursor: pointer;
            transition: background-color 0.2s;
                color: inherit;
    width: fit-content;
        }

        .predefined-msg-btn.selected {
            background-color: #007bff;
            color: white;
            border-color: #007bff;
        }
        .form-group {
            margin-bottom: 1rem;
        }
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
        }
        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ced4da;
            border-radius: 6px;
            font-family: inherit;
        }
        .form-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            margin-top: 1.5rem;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
         
        }
        .btn-primary:hover {
            background-color: #0056b3;
           
        }

        /* استایل‌های لاگ‌های poke */
        .poke-logs-list {
            max-height: 400px;
            overflow-y: auto;
        }
        .poke-log-item {
            border: 1px solid #dee2e6;
            border-radius: 6px;
            margin-bottom: 1rem;
            padding: 1rem;
            background-color: #f8f9fa;
        }
        .poke-log-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
            color: #6c757d;
        }
        .poke-log-message {
            background-color: white;
            padding: 0.75rem;
            border-radius: 4px;
            border: 1px solid #e9ecef;
        }
        .loading, .error, .no-logs {
            text-align: center;
            padding: 2rem;
            color: #6c757d;
        }
        .error {
            color: #dc3545;
        }



        /* ## بهبود استایل فیلترها ## */
        .history-filters {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }
        .filter-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 1rem;
        }
        .advanced-filters {
            display: none;
            flex-direction: column;
            gap: 1rem;
            padding: 1rem;
            border-radius: 6px;
            background-color: #f8f9fa; /* پس‌زمینه متفاوت */
            border: 1px solid var(--border-color);
            margin-top: 0.5rem;
        }
        .filter-row {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }
        @media (min-width: 768px) {
            .filter-row {
                flex-direction: row;
                gap: 1rem;
                align-items: center;
            }
        }
        .filter-row input, .filter-row select {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ccc;
            border-radius: 4px;
        }

        #toggle-filters-btn {
            background: none;
            border: 1px dashed #aaa;
            color: #555;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            flex-grow: 1;
            text-align: right;
            cursor: pointer;
        }
        #toggle-filters-btn .icon {
            float: left;
            transition: transform 0.3s;
        }
        #toggle-filters-btn.open .icon {
            transform: rotate(180deg);
        }
        #clear-filters-btn {
            background: none;
            border: none;
            color: #dc3545;
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0 0.5rem;
        }


        .history-cards-container { max-height: 60vh; overflow-y: auto; padding: 5px; }
        .history-card { background: #fff; border-radius: 8px; border-left: 5px solid #6c757d; margin-bottom: 1rem; padding: 1rem; box-shadow: 0 2px 8px rgba(0,0,0,0.08); }
        .history-card p { margin: 0.5rem 0; }
        .history-card-header { display: flex; justify-content: space-between; align-items: center; }
        .status-badge { padding: 0.2em 0.6em; border-radius: 0.25rem; font-size: 0.8em; }
        .status-converted { background-color: #d1ecf1; color: #0c5460; }
        .status-reported { background-color: #fff3cd; color: #856404; }
        .status-done { background-color: #28a745; color: white; }
        .status-done-confirmed { background-color: #28a745 !important; color: #fff !important; }

        .btn-poke-icon {
            background: none;
            border: none;
            color: #007bff;
            font-size: 1.1rem;
            cursor: pointer;
            padding: 0.25rem;
            border-radius: 4px;
            transition: all 0.2s;
        }
        .btn-poke-icon:hover {
            background-color: #e3f2fd;
            color: #0056b3;
            transform: scale(1.1);
        }

        .recipients-info {
            margin-top: 0.5rem;
            padding: 0.5rem;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            font-size: 0.9rem;
            color: #495057;
        }
        .recipients-info i {
            color: #007bff;
            margin-left: 0.25rem;
        }
        
        #workOrderDetailModal .details-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem; margin-top: 1rem; }
        #workOrderDetailModal .detail-item { margin-bottom: 0.8rem; }
        #workOrderDetailModal .detail-item strong { display: block; color: #555; margin-bottom: 0.3rem; }
        #workOrderDetailModal .detail-full-width { grid-column: 1 / -1; }
        
        .image-modal { z-index: 2000; }
        .image-modal__content { margin: auto; display: block; width: auto; max-width: 90%; max-height: 85vh; animation-name: zoom; animation-duration: 0.4s; }
        .image-modal__close { position: absolute; top: 15px; left: 35px; color: #f1f1f1; font-size: 40px; font-weight: bold; cursor: pointer; }
        @keyframes zoom { from {transform:scale(0.8)} to {transform:scale(1)} }

        /* جلوگیری از اسکرول صفحه پشت مودال */
        body.modal-open {
            overflow: hidden;
            position: fixed;
            width: 100%;
        }
    </style>
</head>
<body id="page-reports-list">

    <div class="container">
        <div class="page-header">
            <h2 class="page-title"><i class="fas fa-inbox"></i> لیست گزارش‌های خرابی</h2>
            <div class="header-actions">
                <button id="history-btn" class="btn btn-secondary"><i class="fas fa-history"></i> مشاهده سوابق</button>
            </div>
        </div>



        <?php if (empty($reports)): ?>
            <div class="no-reports">در حال حاضر هیچ گزارش خرابی برای نمایش وجود ندارد.</div>
        <?php else: ?>
            <div class="report-cards-container">
                <?php foreach ($reports as $report): ?>
                    <div class="report-card-container" data-report-id="<?= $report['id'] ?>">
                        <div class="report-card__inner">
                            <div class="report-card__front <?= $report['urgency'] === 'فوری' ? 'urgent-border' : '' ?>">
                                <div class="report-card__header">
                                    <!-- **MODIFIED**: Added a wrapper div for better structure -->
                                    <div class="report-card__device-info">
                                        <span class="report-card__device-name"><?= htmlspecialchars($report['device_name']) ?></span>
                                        <!-- **ADDED**: Display device location -->
                                        <span class="report-card__location"><i class="fas fa-map-marker-alt"></i> <?= htmlspecialchars($report['location_name'] ?? 'ثبت نشده') ?></span>
                                    </div>
                                    <div class="report-card__header-right">
                                                                                <button class="btn-reminder" data-report-id="<?= $report['id'] ?>" title="یادآوری">
                                            <i class="fas fa-bell"></i>
                                        </button>
                                        <span class="report-card__urgency <?= $report['urgency'] === 'فوری' ? 'urgent' : '' ?>"><?= htmlspecialchars($report['urgency']) ?></span>

                                    </div>
                                </div>
                                <div class="report-card__body">
                                    <p class="report-card__description"><?= nl2br(htmlspecialchars($report['problem_description'])) ?></p>
                                    <div class="report-card__info">
                                        <span><i class="fas fa-calendar-alt"></i> تاریخ خرابی: <?= format_shamsi_datetime($report['breakdown_datetime']) ?></span>
                                        <?php if ($report['line_stoppage_datetime']): ?>
                                            <span class="line-stopped-indicator"><i class="fas fa-power-off"></i> خط متوقف شده است</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="report-card__footer">
                                    <button class="btn-details">جزئیات</button>
                                    <a href="../pages/work_order.php?from_report_id=<?= $report['id'] ?>" class="btn-convert"><i class="fas fa-wrench"></i> تبدیل به دستور کار</a>
                                </div>
                            </div>
                            <div class="report-card__back <?= $report['urgency'] === 'فوری' ? 'urgent-border' : '' ?>">
                                <div class="report-card__header">
                                    <span class="report-card__device-name"><?= htmlspecialchars($report['device_name']) ?></span>
                                    <div class="back-header-buttons">
                                        <button class="btn-poke-logs" data-report-id="<?= $report['id'] ?>"><i class="fas fa-history"></i> یادآوری‌ها</button>
                                        <button class="btn-details-back">بازگشت</button>
                                    </div>
                                </div>
                                <div class="report-card__body-back">
                                    <p><strong>شرح کامل مشکل:</strong><br><?= nl2br(htmlspecialchars($report['problem_description'])) ?></p><hr>
                                    <!-- **ADDED**: Display serial number and location on the back -->
                                    <p><strong>شماره سریال:</strong> <?= htmlspecialchars($report['serial_number'] ?? 'ثبت نشده') ?></p>
                                    <p><strong>محل دستگاه:</strong> <?= htmlspecialchars($report['location_name'] ?? 'ثبت نشده') ?></p>
                                    <p><strong>گزارش دهنده:</strong> <?= htmlspecialchars($report['reporter_name']) ?></p>
                                    <p><strong>زمان دقیق خرابی:</strong> <?= format_shamsi_datetime($report['breakdown_datetime']) ?></p>
                                    <p><strong>زمان ثبت گزارش:</strong> <?= format_shamsi_datetime($report['report_datetime']) ?></p>
                                    <?php if ($report['line_stoppage_datetime']): ?>
                                        <p><strong>زمان دقیق توقف خط:</strong> <?= format_shamsi_datetime($report['line_stoppage_datetime']) ?></p>
                                    <?php endif; ?>
                                    <?php if (!empty($report['images'])): ?>
                                        <div class="image-gallery"><strong>تصاویر پیوست:</strong>
                                            <div>
                                                <?php $images = explode(',', $report['images']); foreach ($images as $imagePath): 
                                                    $fullImagePath = "../" . htmlspecialchars(ltrim($imagePath, "./"));
                                                ?>
                                                    <img src="<?= $fullImagePath ?>" alt="تصویر خرابی" class="gallery-image">
                                                <?php endforeach; ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

    <div id="image-modal" class="image-modal">
        <span class="image-modal__close">&times;</span>
        <img class="image-modal__content" id="modal-image">
    </div>
    
    <div id="history-modal" class="modal">
        <div class="modal-content">
            <span class="close-modal-btn">&times;</span>
            <h3>سوابق گزارش‌های خرابی</h3>
            <div class="history-filters">
                <input type="text" id="history-search" class="form-control" placeholder="جستجو در نام دستگاه، سریال، محل یا شرح مشکل...">
                <div class="filter-actions">
                    <button type="button" id="toggle-filters-btn">فیلترهای پیشرفته <span class="icon">&#9662;</span></button>
                    <button type="button" id="clear-filters-btn" class="btn-clear-filter" title="پاک کردن فیلترها" style="display: none;"><i class="fas fa-undo"></i></button>
                </div>
                <div id="advanced-filters" class="advanced-filters">
                    <div class="filter-row">
                        <input type="text" id="history-from-date" class="form-control" placeholder="از تاریخ">
                        <input type="text" id="history-to-date" class="form-control" placeholder="تا تاریخ">
                    </div>
                    <div class="filter-row">
                        <select id="history-status" class="form-control">
                            <option value="all">همه وضعیت‌ها</option>
                            <option value="گزارش شده">گزارش شده</option>
                            <option value="دستورکار صادر شد">دستورکار صادر شد</option>
                            <option value="انجام شده">انجام شده</option>
                        </select>
                    </div>
                    <button id="history-filter-btn" class="btn btn-primary">اعمال فیلتر</button>
                </div>
            </div>
            <div id="history-cards-container" class="history-cards-container">
                <!-- کارت‌ها با جاوا اسکریپت اینجا اضافه می‌شوند -->
            </div>
        </div>
    </div>

    <!-- مودال جدید برای نمایش جزئیات دستور کار -->
    <div id="workOrderDetailModal" class="modal">
        <div class="modal-content">
            <span class="close-modal-btn">&times;</span>
            <div id="workOrderDetailContent"></div>
        </div>
    </div>

    <!-- مودال یادآوری poke -->
    <div id="pokeModal" class="modal">
    <div class="modal-content" id="pokeModalContent">
            <span class="close-modal-btn">&times;</span>
            <h3>ارسال یادآوری</h3>
            <form id="pokeForm">
                <input type="hidden" id="pokeReportId" name="report_id">

                <div class="form-group">
                    <label>پیام‌های پیش‌فرض:</label>
                    <div class="predefined-messages">
                        <button type="button" class="predefined-msg-btn" data-message="لطفاً بررسی این مورد تسریع شود.">
                            لطفاً بررسی این مورد تسریع شود.
                        </button>
                        <button type="button" class="predefined-msg-btn" data-message="این خرابی هنوز بدون اقدام مانده.">
                            این خرابی هنوز بدون اقدام مانده.
                        </button>
                        <button type="button" class="predefined-msg-btn" data-message="وضعیت این خرابی مشخص نشده. لطفاً پیگیری شود.">
                            وضعیت این خرابی مشخص نشده. لطفاً پیگیری شود.
                        </button>
                    </div>
                </div>

                <div class="form-group">
                    <label for="pokeMessage">پیام سفارشی:</label>
                    <textarea id="pokeMessage" name="message" class="form-control" rows="4" placeholder="پیام خود را وارد کنید..."></textarea>
                    <div id="recipients-info" class="recipients-info">
                        <i class="fas fa-info-circle"></i>
                        <span id="recipients-text">در حال بارگذاری...</span>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">ارسال یادآوری</button>
                    <button type="button" class="btn btn-secondary" onclick="closePokeModal()">انصراف</button>
                </div>
            </form>
        </div>
    </div>

    <!-- مودال نمایش لاگ‌های poke -->
    <div id="pokeLogsModal" class="modal">
        <div class="modal-content">
            <span class="close-modal-btn">&times;</span>
            <h3>تاریخچه یادآوری‌ها</h3>
            <div id="pokeLogsContent">
                <!-- محتوا با جاوا اسکریپت بارگذاری می‌شود -->
            </div>
        </div>
    </div>

    <?php include '../includes/footer.php'; ?>
    
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // ----- منطق چرخش کارت -----
        document.querySelectorAll('.report-card-container').forEach(cardContainer => {
            const innerCard = cardContainer.querySelector('.report-card__inner');
            const viewDetailsBtn = cardContainer.querySelector('.btn-details');
            const backBtn = cardContainer.querySelector('.btn-details-back');
            if (viewDetailsBtn) viewDetailsBtn.addEventListener('click', () => innerCard.classList.add('is-flipped'));
            if (backBtn) backBtn.addEventListener('click', () => innerCard.classList.remove('is-flipped'));
        });

        // ----- منطق مودال نمایش عکس -----
        const imageModal = document.getElementById("image-modal");
        const modalImg = document.getElementById("modal-image");
        const closeImageBtn = document.querySelector(".image-modal__close");
        document.querySelectorAll('.gallery-image').forEach(img => {
            img.addEventListener('click', function() {
                // جلوگیری از اسکرول صفحه پشت
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                document.body.style.top = `-${scrollTop}px`;
                document.body.classList.add('modal-open');

                imageModal.style.display = "block";
                modalImg.src = this.src;
            });
        });
        const closeImageModal = () => {
            imageModal.style.display = "none";
            // بازگرداندن اسکرول صفحه
            document.body.classList.remove('modal-open');
            const scrollTop = parseInt(document.body.style.top || '0') * -1;
            document.body.style.top = '';
            window.scrollTo(0, scrollTop);
        };
        if (closeImageBtn) closeImageBtn.addEventListener('click', closeImageModal);
        
        // ----- منطق مودال سوابق -----
        const historyModal = document.getElementById('history-modal');
        const historyContainer = document.getElementById('history-cards-container');
        const historyFilterBtn = document.getElementById('history-filter-btn');
        const toggleFiltersBtn = document.getElementById('toggle-filters-btn');
        const advancedFilters = document.getElementById('advanced-filters');
        const clearFiltersBtn = document.getElementById('clear-filters-btn');
        
        const historySearchInput = document.getElementById('history-search');
        const historyFromDateInput = document.getElementById('history-from-date');
        const historyToDateInput = document.getElementById('history-to-date');
        const historyStatusInput = document.getElementById('history-status');
        const filterInputs = [historySearchInput, historyFromDateInput, historyToDateInput, historyStatusInput];

        function updateClearButtonVisibility() {
            const isActive = historySearchInput.value.trim() !== '' ||
                                 historyFromDateInput.value.trim() !== '' ||
                                 historyToDateInput.value.trim() !== '' ||
                                 historyStatusInput.value !== 'all';
            clearFiltersBtn.style.display = isActive ? 'inline-block' : 'none';
        }

        document.getElementById('history-btn').addEventListener('click', () => {
            // جلوگیری از اسکرول صفحه پشت
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            document.body.style.top = `-${scrollTop}px`;
            document.body.classList.add('modal-open');

            historyModal.style.display = 'block';
            fetchAndRenderReportHistory();
            updateClearButtonVisibility();
        });

        document.querySelector('#history-modal .close-modal-btn').addEventListener('click', () => {
            historyModal.style.display = 'none';
            // بازگرداندن اسکرول صفحه
            document.body.classList.remove('modal-open');
            const scrollTop = parseInt(document.body.style.top || '0') * -1;
            document.body.style.top = '';
            window.scrollTo(0, scrollTop);
        });

        toggleFiltersBtn.addEventListener('click', () => {
            const isVisible = advancedFilters.style.display === 'flex';
            advancedFilters.style.display = isVisible ? 'none' : 'flex';
            toggleFiltersBtn.classList.toggle('open');
        });

        historyFilterBtn.addEventListener('click', () => {
            fetchAndRenderReportHistory();
            updateClearButtonVisibility();
        });
        
        clearFiltersBtn.addEventListener('click', () => {
            historySearchInput.value = '';
            historyFromDateInput.value = '';
            historyToDateInput.value = '';
            historyStatusInput.value = 'all';
            fetchAndRenderReportHistory();
            updateClearButtonVisibility();
            // بستن آکاردئون
            advancedFilters.style.display = 'none';
            toggleFiltersBtn.classList.remove('open');
        });

        filterInputs.forEach(input => {
            input.addEventListener('input', updateClearButtonVisibility);
            input.addEventListener('change', updateClearButtonVisibility);
        });

        function fetchAndRenderReportHistory() {
            const searchTerm = historySearchInput.value;
            const fromDate = historyFromDateInput.value;
            const toDate = historyToDateInput.value;
            const status = historyStatusInput.value;
            historyContainer.innerHTML = '<p>در حال بارگذاری سوابق...</p>';
            
            const params = new URLSearchParams({
                action: 'get_report_history',
                search: searchTerm,
                from_date: fromDate,
                to_date: toDate,
                status: status
            });

            fetch(`reports_list.php?${params.toString()}`)
            .then(res => res.json())
            .then(data => {
                if (data.success && data.history.length > 0) {
                    let cardsHtml = '';
                    data.history.forEach(item => {
                        let statusClass = '';
                        switch (item.status) {
                            case 'دستورکار صادر شد': statusClass = 'status-converted'; break;
                            case 'انجام شده': statusClass = 'status-done'; break;
                            case 'انجام و تایید شده': statusClass = 'status-done-confirmed'; break;
                            default: statusClass = 'status-reported'; break;
                        }

                        // **MODIFIED**: Added serial number and location to history cards
                        cardsHtml += `
                            <div class="history-card">
                                <div class="history-card-header">
                                    <strong>${item.device_name} (${item.serial_number || 'بدون سریال'})</strong>
                                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                                        <button class="btn-poke-icon" data-report-id="${item.id}" title="یادآوری"><i class="fas fa-hand-point-right"></i></button>
                                        <span class="status-badge ${statusClass}">${item.status}</span>
                                    </div>
                                </div>
                                <p><strong>محل:</strong> ${item.location_name || '-'}</p>
                                <p>${item.problem_description ? item.problem_description : '-'}</p>
                                <p><strong>تاریخ گزارش:</strong> ${item.report_datetime_shamsi}</p>
                                <p><strong>گزارش دهنده:</strong> ${item.reporter_name || '-'}</p>
                                ${item.converted_to_wo_id ? `<p><strong>تبدیل به:</strong> <a href="javascript:void(0)" onclick="showWorkOrderDetailsFromHistory(${item.converted_to_wo_id})">دستور کار شماره ${item.workorder_number || item.converted_to_wo_id}</a></p>` : ''}
                            </div>
                        `;
                    });
                    historyContainer.innerHTML = cardsHtml;

                    // اضافه کردن event listener برای دکمه‌های یادآوری در سوابق
                    document.querySelectorAll('.btn-poke-icon').forEach(btn => {
                        btn.addEventListener('click', function() {
                            const reportId = this.getAttribute('data-report-id');
                            openPokeModal(reportId);
                        });
                    });
                } else {
                    historyContainer.innerHTML = '<p class="no-reports">سابقه‌ای یافت نشد.</p>';
                }
            });
        }

        // تابع کمکی برای باز کردن مودال poke کنار دکمه
        function openPokeModal(reportId) {
            const pokeModal = document.getElementById('pokeModal');
            const pokeModalContent = document.getElementById('pokeModalContent');
            const pokeReportId = document.getElementById('pokeReportId');
            const pokeMessage = document.getElementById('pokeMessage');
            const recipientsText = document.getElementById('recipients-text');

            // پیدا کردن دکمه یادآوری مربوطه
            const btn = document.querySelector('.btn-reminder[data-report-id="' + reportId + '"]') ||
                       document.querySelector('.btn-poke-icon[data-report-id="' + reportId + '"]');

            if (pokeModal && pokeModalContent && pokeReportId && pokeMessage && btn) {
                pokeReportId.value = reportId;
                pokeMessage.value = '';
                document.querySelectorAll('.predefined-msg-btn').forEach(b => b.classList.remove('active'));
                if (recipientsText) {
                    recipientsText.textContent = 'در حال بارگذاری...';
                    loadRecipients(reportId);
                }

                // جلوگیری از اسکرول صفحه پشت
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                document.body.style.top = `-${scrollTop}px`;
                document.body.classList.add('modal-open');

                // نمایش مودال به صورت عادی (نه absolute)
                pokeModal.style.display = 'block';
                pokeModal.style.position = 'fixed';
                pokeModal.style.left = '0';
                pokeModal.style.top = '0';
                pokeModal.style.width = '100%';
                pokeModal.style.height = '100%';
                pokeModal.style.background = 'rgba(0,0,0,0.6)';
                pokeModal.style.zIndex = '10000';

                // تنظیم محتوای مودال
                pokeModalContent.style.margin = '5% auto';
                pokeModalContent.style.width = '90%';
                pokeModalContent.style.maxWidth = '500px';
                pokeModalContent.style.boxShadow = '0 4px 24px rgba(0,0,0,0.12)';
                pokeModalContent.style.border = '1px solid #eee';
                pokeModalContent.style.borderRadius = '10px';
                pokeModalContent.style.padding = '20px';
                pokeModalContent.style.background = '#fff';
                pokeModalContent.style.position = 'relative';
                pokeModalContent.style.display = 'block';
                pokeModalContent.style.boxSizing = 'border-box';
                pokeModalContent.style.direction = 'rtl';
                pokeModalContent.style.textAlign = 'right';
            }
        }

        // تابع بارگذاری لیست گیرندگان
        function loadRecipients(reportId) {
            fetch(`?action=get_recipients&report_id=${reportId}`)
                .then(response => response.json())
                .then(data => {
                    const recipientsText = document.getElementById('recipients-text');
                    if (data.success && recipientsText) {
                        if (data.recipients.length > 0) {
                            recipientsText.textContent = `این پیام به ${data.count} نفر ارسال می‌شود: ${data.recipients.join('، ')}`;
                        } else {
                            recipientsText.textContent = 'هیچ گیرنده‌ای برای ارسال پیام یافت نشد.';
                        }
                    } else {
                        recipientsText.textContent = 'خطا در بارگذاری لیست گیرندگان.';
                    }
                })
                .catch(error => {
                    console.error('Error loading recipients:', error);
                    const recipientsText = document.getElementById('recipients-text');
                    if (recipientsText) {
                        recipientsText.textContent = 'خطا در بارگذاری لیست گیرندگان.';
                    }
                });
        }

        // تابع بستن مودال poke
        function closePokeModal() {
            const pokeModal = document.getElementById('pokeModal');
            if (pokeModal) {
                pokeModal.style.display = 'none';
                // بازگرداندن اسکرول صفحه
                document.body.classList.remove('modal-open');
                const scrollTop = parseInt(document.body.style.top || '0') * -1;
                document.body.style.top = '';
                window.scrollTo(0, scrollTop);
            }
        }

        // ----- منطق دکمه‌های یادآوری اصلی -----
        document.querySelectorAll('.btn-reminder').forEach(btn => {
            btn.addEventListener('click', function() {
                const reportId = this.getAttribute('data-report-id');
                openPokeModal(reportId);
            });
        });

        // ----- منطق هایلایت کردن کارت -----
        const urlParams = new URLSearchParams(window.location.search);
        const highlightId = urlParams.get('highlight');
        if (highlightId) {
            const cardToHighlight = document.querySelector(`.report-card-container[data-report-id="${highlightId}"]`);
            if (cardToHighlight) {
                cardToHighlight.scrollIntoView({ behavior: 'smooth', block: 'center' });
                cardToHighlight.classList.add('highlighted');
                setTimeout(() => {
                    cardToHighlight.classList.remove('highlighted');
                }, 3000);
            }
        }

        // ----- منطق مودال جزئیات دستور کار -----
        const woDetailModal = document.getElementById('workOrderDetailModal');
        const woDetailContent = document.getElementById('workOrderDetailContent');
        
        document.querySelector('#workOrderDetailModal .close-modal-btn').addEventListener('click', () => {
            woDetailModal.style.display = 'none';
            // بازگرداندن اسکرول صفحه
            document.body.classList.remove('modal-open');
            const scrollTop = parseInt(document.body.style.top || '0') * -1;
            document.body.style.top = '';
            window.scrollTo(0, scrollTop);
        });

        window.showWorkOrderDetailsFromHistory = function(workOrderId) {
            woDetailContent.innerHTML = '<p>در حال بارگذاری اطلاعات دستور کار...</p>';
            // جلوگیری از اسکرول صفحه پشت
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            document.body.style.top = `-${scrollTop}px`;
            document.body.classList.add('modal-open');

            woDetailModal.style.display = 'block';

            fetch(`reports_list.php?action=get_work_order_details&id=${workOrderId}`)
                .then(res => res.json())
                .then(data => {
                    if (data.success) {
                        const wo = data.work_order;
                        let assignees = data.assignees.map(a => a.name).join('، ') || 'مشخص نشده';
                        let html = `
                            <h4>جزئیات دستور کار: ${wo.workorder_id}</h4>
                            <div class="details-grid">
                                <div>
                                    <div class="detail-item"><strong>دستگاه:</strong> <span>${wo.device_name || '-'}</span></div>
                                    <div class="detail-item"><strong>نوع:</strong> <span>${wo.type}</span></div>
                                </div>
                                <div>
                                    <div class="detail-item"><strong>تاریخ صدور:</strong> <span>${wo.request_date_shamsi}</span></div>
                                    <div class="detail-item"><strong>تاریخ سررسید:</strong> <span>${wo.due_date_shamsi}</span></div>
                                </div>
                                <div>
                                    <div class="detail-item"><strong>اولویت:</strong> <span>${wo.priority}</span></div>
                                    <div class="detail-item"><strong>وضعیت:</strong> <span>${wo.status}</span></div>
                                </div>
                            </div>
                            <div class="detail-item detail-full-width"><strong>عنوان:</strong> <span>${wo.title}</span></div>
                            <div class="detail-item detail-full-width"><strong>مسئول(ان) اجرا:</strong> <span>${assignees}</span></div>
                            <div class="detail-item detail-full-width"><strong>شرح:</strong> <div>${wo.description.replace(/\n/g, '<br>')}</div></div>
                        `;
                        woDetailContent.innerHTML = html;
                    } else {
                        woDetailContent.innerHTML = `<p>خطا: ${data.message}</p>`;
                    }
                })
                .catch(err => {
                    console.error("Fetch error for WO details:", err);
                    woDetailContent.innerHTML = '<p>خطا در ارتباط با سرور.</p>';
                });
        }

        // بستن مودال‌ها با کلیک روی پس‌زمینه
        window.addEventListener('click', function(event) {
            if (event.target == imageModal) closeImageModal();
            if (event.target == historyModal) historyModal.style.display = 'none';
            if (event.target == woDetailModal) woDetailModal.style.display = 'none';
        });

        // راه‌اندازی DatePicker برای فیلترها
        if (typeof initializeDatepicker === 'function') {
            initializeDatepicker('#history-from-date');
            initializeDatepicker('#history-to-date');
        }

        // ----- منطق مودال poke -----
        const pokeModal = document.getElementById('pokeModal');
        const pokeForm = document.getElementById('pokeForm');
        const pokeMessage = document.getElementById('pokeMessage');
        const predefinedMsgBtns = document.querySelectorAll('.predefined-msg-btn');



        // انتخاب پیام پیش‌فرض
        predefinedMsgBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                predefinedMsgBtns.forEach(b => b.classList.remove('selected'));
                this.classList.add('selected');
                pokeMessage.value = this.getAttribute('data-message');
            });
        });

        // ارسال فرم poke
        pokeForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;

            submitBtn.disabled = true;
            submitBtn.textContent = 'در حال ارسال...';

            fetch('reports_list.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(data.message, 'success');
                    closePokeModal();
                } else {
                    showToast(data.message, 'danger');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('خطا در ارسال یادآوری', 'danger');
            })
            .finally(() => {
                submitBtn.disabled = false;
                submitBtn.textContent = originalText;
            });
        });

        // بستن مودال poke
        window.closePokeModal = closePokeModal;

        // بستن مودال با کلیک روی X
        pokeModal.querySelector('.close-modal-btn').addEventListener('click', closePokeModal);

        // بستن مودال با کلیک خارج از آن
        window.addEventListener('click', function(event) {
            if (event.target == pokeModal) closePokeModal();
        });

        // ----- منطق مودال لاگ‌های poke -----
        const pokeLogsModal = document.getElementById('pokeLogsModal');
        const pokeLogsContent = document.getElementById('pokeLogsContent');

        // باز کردن مودال لاگ‌های poke
        document.querySelectorAll('.btn-poke-logs').forEach(btn => {
            btn.addEventListener('click', function() {
                const reportId = this.getAttribute('data-report-id');
                loadPokeLogs(reportId);
                // جلوگیری از اسکرول صفحه پشت
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                document.body.style.top = `-${scrollTop}px`;
                document.body.classList.add('modal-open');

                pokeLogsModal.style.display = 'block';
            });
        });

        // تابع بارگذاری لاگ‌های poke
        function loadPokeLogs(reportId) {
            pokeLogsContent.innerHTML = '<div class="loading">در حال بارگذاری...</div>';

            fetch(`reports_list.php?action=get_poke_logs&report_id=${reportId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayPokeLogs(data.logs);
                } else {
                    pokeLogsContent.innerHTML = '<div class="error">خطا در بارگذاری لاگ‌ها</div>';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                pokeLogsContent.innerHTML = '<div class="error">خطا در بارگذاری لاگ‌ها</div>';
            });
        }

        // تابع نمایش لاگ‌های poke
        function displayPokeLogs(logs) {
            if (logs.length === 0) {
                pokeLogsContent.innerHTML = '<div class="no-logs">هیچ یادآوری‌ای ثبت نشده است.</div>';
                return;
            }

            let html = '<div class="poke-logs-list">';
            logs.forEach(log => {
                html += `
                    <div class="poke-log-item">
                        <div class="poke-log-header">
                            <span class="sender">از: ${log.sender_name}</span>
                            <span class="recipient">به: ${log.recipient_name}</span>
                            <span class="date">${log.created_at_shamsi}</span>
                        </div>
                        <div class="poke-log-message">${log.message}</div>
                    </div>
                `;
            });
            html += '</div>';
            pokeLogsContent.innerHTML = html;
        }

        // بستن مودال لاگ‌های poke
        window.closePokeLogsModal = function() {
            pokeLogsModal.style.display = 'none';
            // بازگرداندن اسکرول صفحه
            document.body.classList.remove('modal-open');
            const scrollTop = parseInt(document.body.style.top || '0') * -1;
            document.body.style.top = '';
            window.scrollTo(0, scrollTop);
        };

        // بستن مودال با کلیک روی X
        pokeLogsModal.querySelector('.close-modal-btn').addEventListener('click', closePokeLogsModal);

        // بستن مودال با کلیک خارج از آن
        window.addEventListener('click', function(event) {
            if (event.target == pokeLogsModal) closePokeLogsModal();
        });


    });
    </script>
</body>
</html>
